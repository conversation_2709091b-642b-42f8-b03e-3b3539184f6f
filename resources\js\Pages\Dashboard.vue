<script setup>
import { computed, ref, onUnmounted, watch } from 'vue';
import { Head, useForm } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
// import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import EmployeeLayout from '@/Layouts/EmployeeLayout.vue';
import { useBreakpoints } from '@vueuse/core';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Separator } from '@/Components/ui/separator';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import InputError from '@/Components/InputError.vue';
import axios from 'axios';
import LocationMap from '@/Components/LocationMap.vue';
import WeeklyActivityChart from '@/Components/WeeklyActivityChart.vue';
import MonthlyHoursChart from '@/Components/MonthlyHoursChart.vue';
import PunctualityChart from '@/Components/PunctualityChart.vue';

// defineOptions({ layout: EmployeeLayout });

const props = defineProps({
    todaysAssignment: Object,
    currentAttendance: Object,
    monthlyStats: Object,
    recentActivity: Array,
    weeklyActivityData: Object,
    incompleteAttendances: Object,
    weeklyShiftData: Object,
    displayMonth: String,
    weeklyDateRange: Object,
    debugData: Object,
});

// DEBUG: Log debug data to console
if (props.debugData) {
    console.log('=== DASHBOARD DEBUG DATA ===', props.debugData);
}

const showCameraModal = ref(false);
const isCheckingOut = ref(false);
const videoRef = ref(null);
const canvasRef = ref(null);
const streamRef = ref(null);
const selfieDataUrl = ref(null);
// ### THE FIX: Using the correct state variable that is defined
const isProcessing = ref(false); 
const form = useForm({ latitude: null, longitude: null, selfie: null });
const preValidationError = ref('');

// Date range for weekly activity chart
const weeklyStartDate = ref(props.weeklyDateRange?.start_date || '');
const weeklyEndDate = ref(props.weeklyDateRange?.end_date || '');

const startCamera = async () => { try { const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'user' } }); streamRef.value = stream; if (videoRef.value) { videoRef.value.srcObject = stream; } } catch (err) { console.error("Error accessing camera: ", err); alert("Could not access the camera."); showCameraModal.value = false; } };
const stopCamera = () => { if (streamRef.value) { streamRef.value.getTracks().forEach(track => track.stop()); streamRef.value = null; } };
const takeSelfie = () => { if (videoRef.value && canvasRef.value) { const video = videoRef.value; const canvas = canvasRef.value; canvas.width = video.videoWidth; canvas.height = video.videoHeight; const context = canvas.getContext('2d'); context.drawImage(video, 0, 0, canvas.width, canvas.height); selfieDataUrl.value = canvas.toDataURL('image/jpeg'); stopCamera(); } };
const retakeSelfie = () => { selfieDataUrl.value = null; startCamera(); };
const confirmAction = () => { if (!selfieDataUrl.value) { alert("Please take a selfie first."); return; } form.selfie = selfieDataUrl.value; const routeName = isCheckingOut.value ? 'attendance.check-out' : 'attendance.check-in'; const routeParams = isCheckingOut.value ? { attendance: props.currentAttendance.id } : {}; form.post(route(routeName, routeParams), { onSuccess: () => closeModal(), onError: () => { closeModal(); } }); };
const closeModal = () => { stopCamera(); showCameraModal.value = false; form.reset(); form.clearErrors(); };
onUnmounted(() => { stopCamera(); });
const formatMinutes = (minutes) => { if (!minutes) return '0m'; const h = Math.floor(minutes / 60); const m = minutes % 60; let result = ''; if (h > 0) result += `${h}h `; if (m > 0 || h === 0) result += `${m}m`; return result.trim(); };
const formatTime = (time) => { if (!time) return ''; const [h, m] = time.split(':'); const hours = parseInt(h, 10); const minutes = parseInt(m, 10); const ampm = hours >= 12 ? 'PM' : 'AM'; const formattedHours = hours % 12 || 12; const formattedMinutes = minutes < 10 ? '0' + minutes : minutes; return `${formattedHours}:${formattedMinutes} ${ampm}`; };
const statusMessage = computed(() => { if (props.currentAttendance) { const checkInTime = new Date(props.currentAttendance.check_in_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }); return `You are currently checked in since ${checkInTime}.`; } if (props.todaysAssignment) { return 'You are scheduled to work today.'; } return 'You have no shift scheduled for today.'; });
const canTakeAction = computed(() => !!props.todaysAssignment);

// Function to validate and enforce 7-day range
const validateDateRange = () => {
    if (weeklyStartDate.value && weeklyEndDate.value) {
        const startDate = new Date(weeklyStartDate.value);
        const endDate = new Date(weeklyEndDate.value);
        const diffTime = Math.abs(endDate - startDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Ensure exactly 7 days (6 days difference + 1)
        if (diffDays !== 6) {
            // Auto-adjust end date to maintain 7-day range
            const adjustedEndDate = new Date(startDate);
            adjustedEndDate.setDate(startDate.getDate() + 6);
            weeklyEndDate.value = adjustedEndDate.toISOString().split('T')[0];
        }
    }
};

// Function to update weekly activity chart with new date range
const updateWeeklyActivity = () => {
    validateDateRange(); // Ensure 7-day range

    if (weeklyStartDate.value && weeklyEndDate.value) {
        console.log('Sending request with:', {
            weekly_start_date: weeklyStartDate.value,
            weekly_end_date: weeklyEndDate.value,
        });

        router.get('/dashboard', {
            weekly_start_date: weeklyStartDate.value,
            weekly_end_date: weeklyEndDate.value,
        });
    }
};

const beginProcess = (isCheckoutAction) => {
    preValidationError.value = '';
    form.clearErrors();
    isCheckingOut.value = isCheckoutAction;
    isProcessing.value = true; // Use the defined variable
    navigator.geolocation.getCurrentPosition(
        async (position) => {
            const coords = { latitude: position.coords.latitude, longitude: position.coords.longitude, };
            form.latitude = coords.latitude;
            form.longitude = coords.longitude;
            try {
                const validationRoute = isCheckoutAction ? route('attendance.validate-checkout-time', { attendance: props.currentAttendance.id }) : route('attendance.pre-check');
                await axios.post(validationRoute, coords);
                isProcessing.value = false;
                showCameraModal.value = true;
                startCamera();
            } catch (error) {
                if (error.response?.data?.message) {
                    preValidationError.value = error.response.data.message;
                } else {
                    preValidationError.value = 'An unknown validation error occurred.';
                    console.error("Validation Error:", error);
                }
                isProcessing.value = false;
            }
        },
        (error) => {
            preValidationError.value = "Could not get your location. Please enable location services.";
            isProcessing.value = false;
        },
        { enableHighAccuracy: true }
    );
};

// ### THE NEW UX LOGIC ###
const checkoutStatus = computed(() => {
    // If user is not checked in or has no assignment, there's nothing to evaluate.
    if (!props.currentAttendance || !props.todaysAssignment) {
        return { canCheckout: false, message: '' };
    }

    // --- Calculate the exact checkout window ---
    const now = new Date();
    const policy = props.todaysAssignment.shift.policy;
    const shift = props.todaysAssignment.shift;
    
    const checkInTime = new Date(props.currentAttendance.check_in_time);
    let shiftEndTime = new Date(checkInTime.toDateString() + ' ' + shift.end_time);

    // Handle overnight shifts correctly
    if (shift.spans_two_days && shiftEndTime < checkInTime) {
        shiftEndTime.setDate(shiftEndTime.getDate() + 1);
    }

    // Calculate the earliest time the user is allowed to check out
    let earlyCheckoutLimit = new Date(shiftEndTime.getTime());
    earlyCheckoutLimit.setMinutes(shiftEndTime.getMinutes() - policy.early_leave_grace_period_minutes);

    // --- Determine the state and message ---
    if (now < earlyCheckoutLimit) {
        const formattedTime = earlyCheckoutLimit.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        return {
            canCheckout: false,
            message: `You are eligible to check out after ${formattedTime}.`
        };
    }

    // If all checks pass, the user is free to check out.
    return {
        canCheckout: true,
        message: 'You are now eligible to check out.'
    };
});

// New logic to navigate between months
const navigateMonth = (direction) => {
    // 1. Create a date object from the prop string, adding a day to prevent timezone issues.
    // e.g., "June 2025" becomes a Date object for June 1, 2025.
    const current = new Date(`${props.displayMonth} 1`);

    // 2. Safely add or subtract a month.
    current.setMonth(current.getMonth() + direction);

    // 3. Get the new year and month for the request.
    const newYear = current.getFullYear();
    const newMonth = current.getMonth() + 1; // getMonth() is 0-indexed, so add 1

    // 4. Make the Inertia request.
    router.get(route('dashboard'), { 
        month: newMonth, 
        year: newYear 
    }, { 
        preserveState: true, 
        preserveScroll: true 
    });
};
</script>

<template>
    <Head title="Dashboard" />

    <EmployeeLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight text-center sm:text-left">
                Employee Dashboard
            </h2>
        </template>
             <!-- sm:py-12 -->
        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                
                <!-- ### THE NEW, IMPROVED CHECK-IN/OUT SECTION ### -->
                <!-- ### THE UI FIX ### -->
                <!-- ### THE FINAL UI FIX ### -->
                <div class="bg-white dark:bg-gray-800/50 rounded-lg shadow-sm p-6 flex flex-col sm:flex-row items-center justify-between gap-4">
                    <div class="text-center sm:text-left">
                        <h3 v-if="currentAttendance" class="text-lg font-medium">You are currently on the clock.</h3>
                        <h3 v-else-if="todaysAssignment" class="text-lg font-medium">Ready to start your shift?</h3>
                        <h3 v-else class="text-lg font-medium text-muted-foreground">No shift scheduled for today.</h3>
                        
                        <p v-if="preValidationError" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ preValidationError }}</p>
                        <p v-if="currentAttendance && checkoutStatus.message" class="mt-1 text-sm text-muted-foreground">{{ checkoutStatus.message }}</p>
                    </div>

                    <div class="flex-shrink-0">
                        <!-- Use v-if to completely remove the button when not active -->
                        <Button
                            v-if="!currentAttendance"
                            @click="beginProcess(false)"
                            :disabled="!canTakeAction || isProcessing"
                            size="lg"
                            class="px-8 py-6 text-lg"
                            :class="{ 'opacity-25': isProcessing }"
                        >
                            <span v-if="isProcessing">Validating...</span>
                            <span v-else>Check In</span>
                        </Button>

                        <!-- The destructive button needs explicit text color to override defaults -->
                        <Button
                            v-if="currentAttendance"
                            @click="beginProcess(true)"
                            :disabled="!checkoutStatus.canCheckout || isProcessing"
                            variant="destructive"
                            size="lg"
                            class="px-8 py-6 text-lg bg-red-600 text-white hover:bg-red-700"
                        >
                            <span v-if="isProcessing">Validating...</span>
                            <span v-else>Check Out</span>
                        </Button>
                    </div>
                </div>
            
                <div class="px-4 sm:px-0">
                    <Card><CardHeader><CardTitle>Your Status</CardTitle><CardDescription>{{ statusMessage }}</CardDescription></CardHeader><CardContent v-if="todaysAssignment" class="text-sm space-y-2"><div class="flex justify-between items-center"><span class="text-muted-foreground">Shift:</span><span class="font-medium text-right">{{ todaysAssignment.shift.name }} ({{ formatTime(todaysAssignment.shift.start_time) }} - {{ formatTime(todaysAssignment.shift.end_time) }})</span></div><div v-if="todaysAssignment.work_zone" class="flex justify-between items-center"><span class="text-muted-foreground">Work Zone:</span><span class="font-medium">{{ todaysAssignment.work_zone.name }}</span></div><div v-if="currentAttendance" class="flex justify-between items-center text-green-600 dark:text-green-400"><span class="text-muted-foreground">Checked In At:</span><span class="font-medium">{{ new Date(currentAttendance.check_in_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit'}) }}</span></div></CardContent></Card>
                </div>

                <div class="px-4 sm:px-0">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-semibold">Monthly Summary</h3>
                        <div class="flex items-center gap-2">
                            <Button variant="outline" size="sm" @click="navigateMonth(-1)">Prev</Button>
                            <span class="text-sm font-medium w-24 text-center">{{ displayMonth }}</span>
                            <Button variant="outline" size="sm" @click="navigateMonth(1)">Next</Button>
                        </div>
                    </div>
                </div>

                <!-- ### IMPROVED: New 4-column layout with combined overtime/undertime and absent days ### -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 px-4 sm:px-0">
                    <Card>
                        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle class="text-sm font-medium">Scheduled Hours</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p class="text-2xl font-bold">{{ formatMinutes(monthlyStats.total_scheduled_minutes) }}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle class="text-sm font-medium">Time Variance</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div class="space-y-1">
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-green-600">Overtime:</span>
                                    <span class="text-sm font-semibold text-green-600">{{ formatMinutes(monthlyStats.total_overtime_minutes) }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-orange-600">Undertime:</span>
                                    <span class="text-sm font-semibold text-orange-600">{{ formatMinutes(monthlyStats.total_undertime_minutes) }}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle class="text-sm font-medium">Absent Days</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p class="text-2xl font-bold text-gray-500">{{ monthlyStats.absent_days || 0 }}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle class="text-sm font-medium">Late Days</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p class="text-2xl font-bold text-red-500">{{ monthlyStats.days_late }}</p>
                        </CardContent>
                    </Card>
                </div>

                <!-- ### THE FIX: A new 3-column grid for charts and recent activity ### -->
                <div class="grid gap-6 lg:grid-cols-3 px-4 sm:px-0">
                    <!-- The weekly chart now takes up 2 columns -->
                    <Card class="min-w-0 lg:col-span-2">
                        <CardHeader>
                            <div class="flex flex-col gap-3">
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                                    <CardTitle>7-Day Activity</CardTitle>
                                    <div class="flex items-center gap-2 text-sm">
                                        <input
                                            type="date"
                                            v-model="weeklyStartDate"
                                            class="w-32 text-xs px-2 py-1 border border-gray-300 rounded"
                                        />
                                        <span class="text-gray-500 text-xs">to</span>
                                        <input
                                            type="date"
                                            v-model="weeklyEndDate"
                                            class="w-32 text-xs px-2 py-1 border border-gray-300 rounded"
                                        />
                                        <Button @click="updateWeeklyActivity" size="sm" class="text-xs">
                                            Update
                                        </Button>
                                    </div>
                                </div>
                            <div class="flex flex-wrap gap-3 text-xs text-gray-600 mt-2">
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-blue-500 rounded"></div>
                                    <span>Completed</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-orange-500 rounded"></div>
                                    <span>Incomplete</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-gray-300 rounded"></div>
                                    <span>Missing</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-red-600 rounded"></div>
                                    <span>Overtime</span>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent class="h-[300px]">
                            <WeeklyActivityChart
                                :activity-data="weeklyActivityData"
                                :incomplete-attendances="incompleteAttendances"
                                :shift-data="weeklyShiftData"
                            />
                        </CardContent>
                    </Card>

                    <!-- The new Punctuality Chart takes up 1 column -->
                    <Card class="min-w-0">
                        <CardHeader><CardTitle>Monthly Punctuality</CardTitle></CardHeader>
                        <CardContent class="h-[300px]">
                            <div class="h-[220px]">
                                <PunctualityChart :stats="monthlyStats" />
                            </div>
                            <!-- Legend for the punctuality chart -->
                            <div class="grid grid-cols-2 gap-2 text-xs mt-4">
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-green-400 rounded"></div>
                                    <span>On Time ({{ monthlyStats.on_time_days || 0 }})</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-red-400 rounded"></div>
                                    <span>Late ({{ monthlyStats.days_late || 0 }})</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-gray-400 rounded"></div>
                                    <span>Absent ({{ monthlyStats.absent_days || 0 }})</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <div class="w-3 h-3 bg-orange-400 rounded"></div>
                                    <span>Early ({{ monthlyStats.early_days || 0 }})</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
                
                <!-- The recent activity log can now have its own row -->
                <div class="grid grid-cols-1 px-4 sm:px-0">
                    <Card class="min-w-0">
                        <CardHeader>
                            <CardTitle>Recent Activity</CardTitle>
                            <CardDescription>Attendance records for the selected 7-day period.</CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <p v-if="!recentActivity.length" class="text-sm text-muted-foreground pt-4">No recent activity found.</p>
                            <div v-for="item in recentActivity" :key="item.id" class="flex items-center justify-between text-sm">
                                <div class="flex-1">
                                    <p class="font-medium">{{ new Date(item.check_in_time).toLocaleDateString([], { month: 'short', day: 'numeric' }) }}</p>
                                    <p class="text-xs text-muted-foreground">
                                        <span v-if="item.is_placeholder" class="text-gray-500 italic">No attendance recorded</span>
                                        <template v-else>
                                            <span class="font-medium">Actual:</span>
                                            {{ new Date(item.check_in_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) }} -
                                            <span v-if="item.check_out_time">{{ new Date(item.check_out_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) }}</span>
                                            <span v-else class="text-orange-500 italic">Incomplete</span>
                                        </template>
                                    </p>
                                    <p v-if="item.shift" class="text-xs text-muted-foreground">
                                        <span class="font-medium">Expected:</span>
                                        {{ item.shift.start_time.substring(0, 5) }} - {{ item.shift.end_time.substring(0, 5) }}
                                        <span class="text-gray-400">({{ item.shift.name }})</span>
                                    </p>
                                </div>
                                <div class="text-right">
                                    <div v-if="item.is_placeholder" class="font-medium text-gray-500">Absent</div>
                                    <div v-else-if="item.overtime_minutes > 0" class="font-medium text-green-500">+{{ formatMinutes(item.overtime_minutes) }}</div>
                                    <div v-else-if="item.undertime_minutes > 0" class="font-medium text-orange-500">-{{ formatMinutes(item.undertime_minutes) }}</div>
                                    <div v-else-if="!item.check_out_time" class="font-medium text-orange-500">Incomplete</div>
                                    <div v-else class="font-medium text-gray-500">On time</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>

        <Modal :show="showCameraModal" @close="closeModal"><div class="p-6"><h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Action Confirmation</h2><p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Please take a selfie to complete your {{ isCheckingOut ? 'check-out' : 'check-in' }}.</p><InputError class="mt-4" :message="form.errors.selfie" /><div v-if="form.latitude && form.longitude" class="mt-4 rounded-md overflow-hidden border"><LocationMap :latitude="form.latitude" :longitude="form.longitude" /></div><div class="mt-4 bg-gray-200 dark:bg-gray-900 rounded-md aspect-video flex items-center justify-center"><video v-show="!selfieDataUrl" ref="videoRef" autoplay playsinline class="w-full h-full object-cover"></video><canvas ref="canvasRef" class="hidden"></canvas><img v-if="selfieDataUrl" :src="selfieDataUrl" alt="Your Selfie" class="w-full h-full object-cover rounded-md" /></div><div class="mt-6 flex justify-between items-center"><Button v-if="!selfieDataUrl" @click="takeSelfie">Take Selfie</Button><Button v-if="selfieDataUrl" @click="retakeSelfie" variant="outline">Retake</Button><div class="flex gap-2"><SecondaryButton @click="closeModal"> Cancel </SecondaryButton><Button @click="confirmAction" :class="{ 'opacity-25': form.processing }" :disabled="!selfieDataUrl || form.processing">Confirm {{ isCheckingOut ? 'Check-out' : 'Check-in' }}</Button></div></div></div></Modal>
    </EmployeeLayout>
</template>